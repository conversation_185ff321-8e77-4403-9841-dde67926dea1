import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, IconCard } from '@/shared/components/common';
import { CustomerDetailData } from './types';
import SimpleCustomFieldSelector from '../../SimpleCustomFieldSelector';
import CustomFieldRenderer from '../../CustomFieldRenderer';
import { useUpdateCustomerCustomFields } from '../../../hooks/useCustomerQuery';
import { MetadataFieldDto } from '../../../services/customer.service';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number; // ID của custom field từ mockAvailableFields
  fieldId: number; // Alias cho id để tương thích với CustomFieldRenderer
  configId: string; // configId để gửi lên API
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}



interface CustomerCustomFieldsProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị trường tùy chỉnh của khách hàng
 */
const CustomerCustomFields: React.FC<CustomerCustomFieldsProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Hooks
  const updateCustomFieldsMutation = useUpdateCustomerCustomFields();

  // State
  const [customerCustomFields, setCustomerCustomFields] = useState<SelectedCustomField[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Derived state
  const isSaving = updateCustomFieldsMutation.isPending;

  // Thêm trường tùy chỉnh vào khách hàng
  const handleToggleCustomFieldToCustomer = useCallback(
    (fieldId: number, fieldData: Record<string, unknown>) => {
      console.log('🔍 Toggle custom field:', { fieldId, fieldData });
      
      setCustomerCustomFields(prev => {
        // Kiểm tra xem field đã tồn tại chưa
        const existingIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingIndex >= 0) {
          // Nếu đã tồn tại, xóa nó (toggle off)
          setHasChanges(true);
          return prev.filter(field => field.fieldId !== fieldId);
        }

        console.log('🔍 Field data received:', fieldData);
        console.log('🔍 ConfigJson from fieldData:', fieldData?.['configJson']);
        console.log('🔍 ConfigJson type:', typeof fieldData?.['configJson']);
        console.log('🔍 ConfigJson stringified:', JSON.stringify(fieldData?.['configJson'], null, 2));

        // Thêm trường mới với thông tin đầy đủ
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        // Xác định giá trị mặc định dựa trên type
        let defaultValue: string | number | boolean = '';
        if (fieldType === 'boolean' || fieldComponent === 'boolean') {
          defaultValue = false; // Boolean mặc định là false
        } else if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0; // Number mặc định là 0
        }

        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          configId: (fieldData?.['configId'] as string) || fieldId.toString(),
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        console.log('🔍 New field created:', newField);
        console.log('🔍 New field configJson:', newField.configJson);

        setHasChanges(true);
        return [...prev, newField];
      });
    },
    []
  );

  console.log('🔍 Customer custom fields:', customerCustomFields);
  console.log('🔍 Customer custom fields with configJson details:',
    customerCustomFields.map(field => ({
      id: field.id,
      label: field.label,
      type: field.type,
      component: field.component,
      configJson: field.configJson,
      configJsonStringified: JSON.stringify(field.configJson, null, 2)
    }))
  );



  // Cập nhật giá trị trường tùy chỉnh trong khách hàng
  const handleUpdateCustomFieldInCustomer = useCallback((customFieldId: number, value: string | number | boolean) => {
    console.log('🔍 handleUpdateCustomFieldInCustomer:', {
      customFieldId,
      value,
      valueType: typeof value,
      valueString: String(value)
    });

    setCustomerCustomFields(prev => prev.map(field => {
      if (field.id === customFieldId) {
        const updatedField = { ...field, value: { value } };
        console.log('🔍 Updated field:', {
          fieldId: field.id,
          fieldLabel: field.label,
          fieldType: field.type,
          oldValue: field.value,
          newValue: { value },
          updatedField
        });
        return updatedField;
      }
      return field;
    }));
    setHasChanges(true);
  }, []);

  // Xóa trường tùy chỉnh khỏi khách hàng
  const handleRemoveCustomFieldFromCustomer = useCallback((customFieldId: number) => {
    setCustomerCustomFields(prev => prev.filter(field => field.id !== customFieldId));
    setHasChanges(true);
  }, []);

  // Handle save custom fields
  const handleSaveCustomFields = useCallback(async () => {
    try {
      // Convert to API format - extract actual value from {value: ...} structure
      const metadata: MetadataFieldDto[] = customerCustomFields.map(field => ({
        configId: field.configId || field.id.toString(),
        value: field.value['value'] // Extract the actual value from the wrapper object
      }));

      console.log('🔍 Sending metadata to API:', metadata);

      // Call API
      await updateCustomFieldsMutation.mutateAsync({
        id: parseInt(customer.id),
        data: { metadata }
      });

      // Reset changes flag
      setHasChanges(false);

    } catch (error) {
      console.error('Error saving custom fields:', error);
    }
  }, [customerCustomFields, customer.id, updateCustomFieldsMutation]);



  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('business:customer.detail.customFields', 'Trường tùy chỉnh')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* SimpleCustomFieldSelector thay thế cho table */}
        <SimpleCustomFieldSelector
          onFieldSelect={fieldData => {
            handleToggleCustomFieldToCustomer(
              fieldData.id,
              fieldData as unknown as Record<string, unknown>
            );
          }}
          selectedFieldIds={customerCustomFields.map(f => f.fieldId)}
          placeholder={t(
            'business:customer.form.customFields.searchPlaceholder',
            'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
          )}
        />

        {customerCustomFields.length > 0 && (
          <div className="space-y-3">
            {customerCustomFields.map((field) => {
              // Xử lý value đúng cho tất cả kiểu dữ liệu
              const fieldValue = field.value['value'];
              let processedValue: string | number | boolean;

              if (fieldValue !== undefined && fieldValue !== null) {
                processedValue = fieldValue as string | number | boolean;
              } else {
                // Giá trị mặc định dựa trên type
                if (field.type === 'boolean' || field.component === 'boolean') {
                  processedValue = false;
                } else if (field.type === 'number' || field.component === 'number') {
                  processedValue = 0;
                } else {
                  processedValue = '';
                }
              }

              return (
                <CustomFieldRenderer
                  key={field.id}
                  field={field}
                  value={processedValue}
                  onChange={(value) => handleUpdateCustomFieldInCustomer(field.id, value)}
                  onRemove={() => handleRemoveCustomFieldFromCustomer(field.id)}
                />
              );
            })}
          </div>
        )}

        {customerCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:customer.form.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.')}
            </Typography>
          </div>
        )}

        {/* Save button - only show if there are changes */}
        {hasChanges && customerCustomFields.length > 0 && (
          <div className="flex justify-end">
            <IconCard
              icon="check"
              onClick={handleSaveCustomFields}
              disabled={isSaving}
              variant="primary"
              title={t('common:save')}
              size="md"
              isLoading={isSaving}
            />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default CustomerCustomFields;
